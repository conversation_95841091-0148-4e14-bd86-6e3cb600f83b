#!/usr/bin/env python3
"""
前端功能测试脚本

使用Selenium测试前端页面的JavaScript功能和API交互
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

def test_api_endpoints(base_url="http://localhost:8003"):
    """测试API端点是否正常工作"""
    print("🧪 测试API端点...")
    
    endpoints = [
        "/",
        "/health", 
        "/api/info",
        "/api/prediction/models",
        "/api/adaptive/status",
        "/api/dataset/info"
    ]
    
    results = {}
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            results[endpoint] = {
                "status": response.status_code,
                "success": response.status_code == 200,
                "data": response.json() if response.headers.get('content-type', '').startswith('application/json') else None
            }
            print(f"✅ {endpoint}: {response.status_code}")
        except Exception as e:
            results[endpoint] = {
                "status": "error",
                "success": False,
                "error": str(e)
            }
            print(f"❌ {endpoint}: {e}")
    
    return results

def test_frontend_javascript(base_url="http://localhost:8003"):
    """测试前端JavaScript功能"""
    print("🌐 测试前端JavaScript功能...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    driver = None
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        # 访问前端页面
        frontend_url = f"{base_url}/static/index.html"
        print(f"访问页面: {frontend_url}")
        driver.get(frontend_url)
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "container"))
        )
        
        # 检查页面标题
        title = driver.title
        print(f"页面标题: {title}")
        
        # 检查JavaScript错误
        logs = driver.get_log('browser')
        js_errors = [log for log in logs if log['level'] == 'SEVERE']
        
        if js_errors:
            print("❌ JavaScript错误:")
            for error in js_errors:
                print(f"  - {error['message']}")
        else:
            print("✅ 没有JavaScript错误")
        
        # 测试按钮点击
        test_button_clicks(driver)
        
        # 测试API调用
        test_api_calls(driver)
        
        return {
            "success": True,
            "title": title,
            "js_errors": js_errors,
            "url": frontend_url
        }
        
    except WebDriverException as e:
        print(f"❌ 浏览器错误: {e}")
        print("💡 提示: 请确保已安装Chrome浏览器和ChromeDriver")
        return {
            "success": False,
            "error": "浏览器配置问题",
            "details": str(e)
        }
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }
    finally:
        if driver:
            driver.quit()

def test_button_clicks(driver):
    """测试按钮点击功能"""
    print("🖱️ 测试按钮点击...")
    
    try:
        # 测试标签页切换
        tab_buttons = driver.find_elements(By.CLASS_NAME, "tab-btn")
        print(f"找到 {len(tab_buttons)} 个标签页按钮")
        
        if tab_buttons:
            # 点击第二个标签页（模型预测）
            if len(tab_buttons) > 1:
                tab_buttons[1].click()
                time.sleep(1)
                print("✅ 标签页切换成功")
        
        # 测试自适应微调按钮
        try:
            refresh_btn = driver.find_element(By.ID, "refresh-status-btn")
            refresh_btn.click()
            time.sleep(2)
            print("✅ 刷新状态按钮点击成功")
        except Exception as e:
            print(f"⚠️ 刷新状态按钮测试失败: {e}")
        
    except Exception as e:
        print(f"❌ 按钮点击测试失败: {e}")

def test_api_calls(driver):
    """测试前端API调用"""
    print("📡 测试前端API调用...")
    
    try:
        # 执行JavaScript来测试API调用
        script = """
        return new Promise((resolve) => {
            if (window.API) {
                window.API.getHealth().then(result => {
                    resolve({success: true, api_available: true, health: result});
                }).catch(error => {
                    resolve({success: false, api_available: true, error: error.message});
                });
            } else {
                resolve({success: false, api_available: false, error: 'API对象不存在'});
            }
        });
        """
        
        result = driver.execute_async_script(script)
        
        if result['success']:
            print("✅ 前端API调用成功")
            print(f"  健康检查结果: {result.get('health', {}).get('status', 'unknown')}")
        else:
            print(f"❌ 前端API调用失败: {result.get('error', 'unknown')}")
            if not result.get('api_available'):
                print("  原因: API对象未正确初始化")
        
        return result
        
    except Exception as e:
        print(f"❌ API调用测试失败: {e}")
        return {"success": False, "error": str(e)}

def main():
    """主函数"""
    print("🔍 前端功能测试开始")
    print("=" * 50)
    
    base_url = "http://localhost:8003"
    
    # 1. 测试API端点
    api_results = test_api_endpoints(base_url)
    
    # 2. 测试前端JavaScript
    frontend_results = test_frontend_javascript(base_url)
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    # API测试结果
    api_success_count = sum(1 for r in api_results.values() if r.get('success'))
    print(f"API端点测试: {api_success_count}/{len(api_results)} 通过")
    
    # 前端测试结果
    if frontend_results.get('success'):
        print("✅ 前端JavaScript测试: 通过")
        if frontend_results.get('js_errors'):
            print(f"⚠️ JavaScript错误数量: {len(frontend_results['js_errors'])}")
    else:
        print("❌ 前端JavaScript测试: 失败")
        print(f"  错误: {frontend_results.get('error', 'unknown')}")
    
    print(f"\n🌐 前端页面地址: {base_url}/static/index.html")
    print(f"📚 API文档地址: {base_url}/docs")

if __name__ == "__main__":
    main()
