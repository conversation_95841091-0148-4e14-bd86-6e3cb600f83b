#!/usr/bin/env python3
"""
前端修复验证脚本

验证前端与后端交互问题是否已完全修复
"""

import requests
import time
import subprocess
import sys
from pathlib import Path

def test_api_endpoints(base_url="http://localhost:8000"):
    """测试所有API端点"""
    print("🧪 测试API端点...")
    
    endpoints = {
        "根路径": "/",
        "健康检查": "/health", 
        "API信息": "/api/info",
        "模型列表": "/api/models",
        "自适应状态": "/api/adaptive/status",
        "数据集信息": "/api/dataset/info"
    }
    
    results = {}
    for name, endpoint in endpoints.items():
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            success = response.status_code == 200
            results[name] = {
                "endpoint": endpoint,
                "status": response.status_code,
                "success": success
            }
            
            if success:
                print(f"✅ {name} ({endpoint}): {response.status_code}")
                if endpoint == "/api/models":
                    data = response.json()
                    print(f"   模型数量: {data.get('total_count', 0)}")
            else:
                print(f"❌ {name} ({endpoint}): {response.status_code}")
                
        except Exception as e:
            results[name] = {
                "endpoint": endpoint,
                "status": "error",
                "success": False,
                "error": str(e)
            }
            print(f"❌ {name} ({endpoint}): {e}")
    
    return results

def check_javascript_files():
    """检查JavaScript文件是否存在语法错误"""
    print("\n📝 检查JavaScript文件...")
    
    js_files = [
        "static/js/api.js",
        "static/js/main.js", 
        "static/js/adaptive.js",
        "static/js/prediction.js",
        "static/js/comparison.js"
    ]
    
    results = {}
    for js_file in js_files:
        file_path = Path(js_file)
        if file_path.exists():
            print(f"✅ {js_file}: 文件存在")
            results[js_file] = {"exists": True, "size": file_path.stat().st_size}
        else:
            print(f"❌ {js_file}: 文件不存在")
            results[js_file] = {"exists": False}
    
    return results

def check_html_files():
    """检查HTML文件"""
    print("\n🌐 检查HTML文件...")
    
    html_files = [
        "static/index.html",
        "static/test.html"
    ]
    
    results = {}
    for html_file in html_files:
        file_path = Path(html_file)
        if file_path.exists():
            print(f"✅ {html_file}: 文件存在")
            results[html_file] = {"exists": True, "size": file_path.stat().st_size}
        else:
            print(f"❌ {html_file}: 文件不存在")
            results[html_file] = {"exists": False}
    
    return results

def verify_fixes():
    """验证具体的修复内容"""
    print("\n🔧 验证修复内容...")
    
    fixes = {}
    
    # 1. 检查main.js语法修复
    main_js_path = Path("static/js/main.js")
    if main_js_path.exists():
        content = main_js_path.read_text(encoding='utf-8')
        # 检查是否还有多余的闭合大括号
        lines = content.split('\n')
        if len(lines) > 470:
            # 检查第469-471行附近
            context = lines[467:472] if len(lines) > 472 else lines[467:]
            has_extra_brace = any(line.strip() == '}' and i > 0 and lines[467+i-1].strip() == '}' for i, line in enumerate(context))
            fixes["main_js_syntax"] = {
                "fixed": not has_extra_brace,
                "description": "移除多余的闭合大括号"
            }
            print(f"{'✅' if not has_extra_brace else '❌'} main.js语法修复: {'已修复' if not has_extra_brace else '仍有问题'}")
        else:
            fixes["main_js_syntax"] = {"fixed": True, "description": "文件长度正常"}
            print("✅ main.js语法修复: 文件长度正常")
    
    # 2. 检查API端点路径修复
    api_js_path = Path("static/js/api.js")
    if api_js_path.exists():
        content = api_js_path.read_text(encoding='utf-8')
        # 检查是否使用正确的API路径
        correct_models_path = "PREDICTION_MODELS: '/api/models'" in content
        fixes["api_endpoints"] = {
            "fixed": correct_models_path,
            "description": "修正API端点路径"
        }
        print(f"{'✅' if correct_models_path else '❌'} API端点路径修复: {'已修复' if correct_models_path else '仍有问题'}")
    
    return fixes

def main():
    """主函数"""
    print("🔍 前端修复验证开始")
    print("=" * 60)
    
    # 检查文件
    js_results = check_javascript_files()
    html_results = check_html_files()
    
    # 验证修复
    fix_results = verify_fixes()
    
    # 提示启动服务器
    print("\n🚀 请启动服务器进行API测试:")
    print("   python start_server.py --dev")
    print("\n等待服务器启动后按Enter继续...")
    input()
    
    # 测试API
    api_results = test_api_endpoints()
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📊 验证结果总结:")
    
    # 文件检查结果
    js_count = sum(1 for r in js_results.values() if r.get('exists'))
    html_count = sum(1 for r in html_results.values() if r.get('exists'))
    print(f"JavaScript文件: {js_count}/{len(js_results)} 存在")
    print(f"HTML文件: {html_count}/{len(html_results)} 存在")
    
    # 修复验证结果
    fix_count = sum(1 for r in fix_results.values() if r.get('fixed'))
    print(f"修复验证: {fix_count}/{len(fix_results)} 通过")
    
    # API测试结果
    api_count = sum(1 for r in api_results.values() if r.get('success'))
    print(f"API端点测试: {api_count}/{len(api_results)} 通过")
    
    # 总体评估
    total_checks = len(js_results) + len(html_results) + len(fix_results) + len(api_results)
    passed_checks = js_count + html_count + fix_count + api_count
    
    print(f"\n🎯 总体通过率: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
    
    if passed_checks == total_checks:
        print("🎉 所有检查通过！前端与后端交互问题已完全修复。")
    else:
        print("⚠️ 仍有部分问题需要解决。")
    
    print(f"\n🌐 测试页面:")
    print(f"   主页面: http://localhost:8000/static/index.html")
    print(f"   测试页面: http://localhost:8000/static/test.html")
    print(f"   API文档: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
