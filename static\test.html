<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 前端功能测试</h1>
    
    <div class="test-section">
        <h2>1. JavaScript基础测试</h2>
        <button class="test-button" onclick="testBasicJS()">测试基础JavaScript</button>
        <div id="basic-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. API对象测试</h2>
        <button class="test-button" onclick="testAPIObject()">测试API对象</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 健康检查API测试</h2>
        <button class="test-button" onclick="testHealthAPI()">测试健康检查</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 模型列表API测试</h2>
        <button class="test-button" onclick="testModelsAPI()">测试模型列表</button>
        <div id="models-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. 自适应状态API测试</h2>
        <button class="test-button" onclick="testAdaptiveAPI()">测试自适应状态</button>
        <div id="adaptive-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>6. 事件绑定测试</h2>
        <button class="test-button" onclick="testEventBinding()">测试事件绑定</button>
        <div id="event-result" class="result"></div>
    </div>

    <!-- 引入API脚本 -->
    <script src="js/api.js"></script>
    <script src="js/adaptive.js"></script>
    <script src="js/prediction.js"></script>
    <script src="js/comparison.js"></script>
    <script src="js/main.js"></script>

    <script>
        // 测试函数
        function testBasicJS() {
            const resultDiv = document.getElementById('basic-result');
            try {
                // 测试基本JavaScript功能
                const testObj = {
                    message: "JavaScript正常工作",
                    timestamp: new Date().toISOString(),
                    random: Math.random()
                };
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ JavaScript基础测试通过</strong><br>
                    <pre>${JSON.stringify(testObj, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ JavaScript基础测试失败:</strong> ${error.message}`;
            }
        }

        function testAPIObject() {
            const resultDiv = document.getElementById('api-result');
            try {
                if (typeof window.API === 'undefined') {
                    throw new Error('API对象未定义');
                }
                
                const apiMethods = Object.keys(window.API);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ API对象测试通过</strong><br>
                    <strong>可用方法:</strong><br>
                    <pre>${apiMethods.join('\n')}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ API对象测试失败:</strong> ${error.message}`;
            }
        }

        async function testHealthAPI() {
            const resultDiv = document.getElementById('health-result');
            try {
                resultDiv.innerHTML = '⏳ 正在测试健康检查API...';
                
                const result = await window.API.getHealth();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ 健康检查API测试通过</strong><br>
                    <strong>状态:</strong> ${result.status}<br>
                    <strong>响应数据:</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ 健康检查API测试失败:</strong> ${error.message}`;
            }
        }

        async function testModelsAPI() {
            const resultDiv = document.getElementById('models-result');
            try {
                resultDiv.innerHTML = '⏳ 正在测试模型列表API...';
                
                const result = await window.API.getAvailableModels();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ 模型列表API测试通过</strong><br>
                    <strong>模型数量:</strong> ${result.total_count || 0}<br>
                    <strong>响应数据:</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ 模型列表API测试失败:</strong> ${error.message}`;
            }
        }

        async function testAdaptiveAPI() {
            const resultDiv = document.getElementById('adaptive-result');
            try {
                resultDiv.innerHTML = '⏳ 正在测试自适应状态API...';
                
                const result = await window.API.getAdaptiveStatus();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ 自适应状态API测试通过</strong><br>
                    <strong>响应数据:</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ 自适应状态API测试失败:</strong> ${error.message}`;
            }
        }

        function testEventBinding() {
            const resultDiv = document.getElementById('event-result');
            try {
                // 检查是否有管理器对象
                const managers = [];
                if (window.adaptiveManager) managers.push('adaptiveManager');
                if (window.predictionManager) managers.push('predictionManager');
                if (window.comparisonManager) managers.push('comparisonManager');
                if (window.app) managers.push('app');
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ 事件绑定测试通过</strong><br>
                    <strong>已初始化的管理器:</strong><br>
                    <pre>${managers.join('\n') || '无'}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ 事件绑定测试失败:</strong> ${error.message}`;
            }
        }

        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成');
            setTimeout(() => {
                testBasicJS();
                testAPIObject();
            }, 1000);
        });
    </script>
</body>
</html>
